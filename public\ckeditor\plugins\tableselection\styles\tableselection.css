.cke_table-faked-selection-editor *::selection, table[data-cke-table-faked-selection-table] *::selection {
	background: transparent;
}

.cke_table-faked-selection {
	background: darkgray !important;
	color: black;
}
.cke_table-faked-selection a {
	color: black;
}
.cke_editable:focus .cke_table-faked-selection {
	/* We have to use !important here, as td might specify it's own background, thus table selection
	would not be visible. */
	background: #0076cb !important;
	color: white;
}
.cke_editable:focus .cke_table-faked-selection a {
	color: white;
}
.cke_table-faked-selection::-moz-selection, .cke_table-faked-selection ::-moz-selection {
	background: transparent;
}
.cke_table-faked-selection::selection, .cke_table-faked-selection ::selection {
	background: transparent;
}
