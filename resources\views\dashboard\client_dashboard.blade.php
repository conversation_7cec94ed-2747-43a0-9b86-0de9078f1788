@extends('layouts.layout')
@section('content')
    <div class="container-fluid flex-grow-1 container-p-y">
        <div class="row gy-5 gx-5">
            <div class="col-12 col-md-12 col-lg-6 col-xxl-4">
                <div class="card">
                    <div
                        class="card-body position-relative d-flex flex-column flex-md-row align-items-center justify-content-between">
                        <div class="d-flex align-items-center gap-2">
                            <div class="avatar">
                                <img src="{{ isset($profile_img) && $profile_img ? asset('storage/profile_images/' . $profile_img) : asset('assets/img/avatars/1.png') }}"
                                    alt="User Image" class="w-px-40 h-auto rounded-circle" />
                            </div>
                            <h5 class="card-title mb-0"> Welcome Back,
                                <span class="text-primary">{{ $name ?? '' }}</span>
                            </h5>
                        </div>
                        <p class="mb-2 mt-2">
                        </p>
                        <a class="btn btn-sm btn-primary rounded text-white myProfileLink">View Profile</a>
                    </div>
                </div>
            </div>

            <div class="col-12 col-md-12 col-lg-12 col-xxl-12">
                <div class="row gy-5 gx-5">
                    <div class="col-12 col-md-12 col-lg-6 col-xxl-7">
                        <div class="card mb-0 h-100">
                            <div class="card-header d-flex align-items-center justify-content-between">
                                <h5 class="card-title m-0 me-2">Projects</h5>
                                <a href="{{ Route('projects') }}" class="btn btn-sm border-primary bg-label-primary"> View
                                    All <i class="mdi mdi-arrow-right"></i></a>
                            </div>
                            <div class="card-body overflow-hidden position-relative" style="min-height: 300px">
                                <div class="row gy-5 gx-5">
                                    @forelse ($selectedProjects as $project)
                                        <div class="col-12 col-md-6 col-lg-6 col-xxl-6">
                                            <div class="card mb-0 shadow-none border">
                                                <div class="card-body">
                                                    <h4 class="text-primary">
                                                        <a href="javascript:void(0);">{{ $project['project_name'] }}</a>
                                                    </h4>
                                                    <div class="d-flex align-items-center mb-3">
                                                        <a href="javascript:void(0);" class="avatar">
                                                            <img src="../assets/img/avatars/1.png" class="img-fluid rounded"
                                                                alt="img">
                                                        </a>
                                                        <div class="ms-2">
                                                            <h6 class="fw-bold mb-0">
                                                                <a class="text-black" href="javascript:void(0);">
                                                                    {{ $project['team_leader_name'] ?? 'Not Assigned' }}
                                                                </a>
                                                            </h6>
                                                            <span class="d-block">Project Leader</span>
                                                        </div>
                                                    </div>

                                                    <div class="d-flex align-items-center mb-3">
                                                        <a href="javascript:void(0);"
                                                            class="avatar bg-label-success rounded d-flex justify-content-center align-items-center">
                                                            <i class="mdi mdi-calendar mdi-24px"></i>
                                                        </a>
                                                        <div class="ms-2">
                                                            <h6 class="fw-bold mb-0">
                                                                {{ \Carbon\Carbon::parse($project['start_date'])->format('d M Y') }}
                                                            </h6>
                                                            <span class="d-block">Start Date</span>
                                                        </div>
                                                    </div>

                                                    <div
                                                        class="d-flex align-items-center justify-content-between bg-transparent border border-dashed rounded p-2 mb-3">
                                                        <div class="d-flex align-items-center">
                                                            <span
                                                                class="avatar-sm bg-label-primary rounded me-2 d-flex justify-content-center align-items-center">
                                                                <i class="mdi mdi-playlist-check"></i>
                                                            </span>
                                                            <p class="mb-0 fw-semibold">
                                                                Tasks :
                                                                <span
                                                                    class="text-success">{{ $project['completedTasksCount'] }}</span>
                                                                <span
                                                                    class="text-light">/{{ $project['totalTasks'] }}</span>
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @empty
                                        <!-- Data not found -->
                                        <div
                                            class="d-flex justify-content-center align-items-center position-absolute top-0 start-0 w-100 h-100">
                                            <img src="../assets/img/illustrations/notfouund.svg" class="img-fluid"
                                                alt="img">
                                        </div>
                                    @endforelse
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-md-6 col-lg-6 col-xxl-5">
                        <div class="card mb-0">
                            <div class="card-header d-flex align-items-center justify-content-between">
                                <h5 class="card-title m-0 me-2">Tasks</h5>
                                <a href="{{ Route('tasks') }}" class="btn btn-sm border-primary bg-label-primary"> View All
                                    <i class="mdi mdi-arrow-right"></i></a>
                            </div>
                            <div class="card-body overflow-hidden position-relative" style="min-height: 300px">
                                @forelse ($selectedTasks as $tasks)
                                    <div class="list-group-item border rounded mb-2 p-2">
                                        <div class="row align-items-center row-gap-3">
                                            <div class="col-md-8">
                                                <div class="d-flex align-items-center flex-wrap row-gap-3">
                                                    <span><i class="mdi mdi-dots-grid me-2"></i></span>
                                                    <h6 class="mb-0">
                                                        {{ $tasks['title'] }}
                                                    </h6>
                                                </div>
                                            </div>

                                            <div class="col-md-4">
                                                <div
                                                    class="d-flex align-items-center justify-content-md-end flex-wrap row-gap-3">
                                                    <span class="badge rounded bg-label-secondary px-2 py-1">
                                                        <i class="mdi mdi-circle-medium"></i>
                                                        {{ $tasks['status_name'] }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @empty
                                    <!-- Data not found -->
                                    <div
                                        class="d-flex justify-content-center align-items-center position-absolute top-0 start-0 w-100 h-100">
                                        <img src="../assets/img/illustrations/notfouund.svg" class="img-fluid"
                                            alt="img">
                                    </div>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
