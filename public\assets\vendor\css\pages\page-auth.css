.background-radial-gradient {
  min-height: 100vh;
  background-color: hsl(218, 41%, 15%);
  background-image: radial-gradient(650px circle at 0% 0%,
      hsl(218, 41%, 35%) 15%,
      hsl(218, 41%, 30%) 35%,
      hsl(218, 41%, 20%) 75%,
      hsl(218, 41%, 19%) 80%,
      transparent 100%),
    radial-gradient(1250px circle at 100% 100%,
      hsl(218, 41%, 45%) 15%,
      hsl(218, 41%, 30%) 35%,
      hsl(218, 41%, 20%) 75%,
      hsl(218, 41%, 19%) 80%,
      transparent 100%);
}

.background-radial-gradient .container {
  min-height: 100vh;
}

#radius-shape-1 {
  height: 120px;
  width: 120px;
  top: -40px;
  left: -60px;
  background: radial-gradient(#44006b, #ad1fff);
  overflow: hidden;
}

#radius-shape-2 {
  border-radius: 38% 62% 63% 37% / 70% 33% 67% 30%;
  bottom: -50px;
  right: -80px;
  width: 200px;
  height: 200px;
  background: radial-gradient(#44006b, #ad1fff);
  overflow: hidden;
}

.authentication-wrapper {
  position: relative;
  display: flex;
  flex-basis: 100%;
  min-height: 100vh;
  width: 100%;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  padding: 5rem 0 !important;
  background-color: #f1f1f1;
}

.authentication-wrapper .login_container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #891AB4;
  box-shadow: 0 .4rem .8rem rgb(0 0 0 / 30%);
  gap: 2rem;
  padding-right: 2rem;
  margin: 1rem;
  overflow: hidden;
}

@media (max-width: 900px) {
  .authentication-wrapper .login_container {
    width: 90%;
    padding: 2rem 2rem;
    justify-content: center;
  }
}

@media (max-width: 425px) {
  .authentication-wrapper .login_container {
    padding: 0;
  }
}

.authentication-wrapper .image-section img {
  width: 100%;
  height: auto;
  max-width: 400px;
  object-fit: cover;
}

@media (max-width: 900px) {
  .authentication-wrapper .image-section {
    display: none;
  }

  .authentication-wrapper .image-section img {
    display: none;
  }
}

.authentication-wrapper .authentication-inner {
  max-width: 425px;
  width: 100%;
  position: relative;
  /* bottom: 5rem; */
}

@media (max-width: 425px) {
  .authentication-wrapper .authentication-inner {
    bottom: 0;
  }
}

.authentication-wrapper.authentication-basic {
  align-items: center;
  justify-content: center;
}

.authentication-wrapper .auth-cover-illustration {
  z-index: 1;
  max-inline-size: 38rem;
}

.authentication-wrapper .authentication-image-object-left {
  position: absolute;
  bottom: 6%;
  left: 4%;
}

.authentication-wrapper .authentication-image-object-right {
  position: absolute;
  bottom: 7%;
  right: 4%;
}

.authentication-wrapper .authentication-image {
  z-index: -1;
  inline-size: 100%;
  position: absolute;
  inset-inline-start: 0;
  bottom: 0;
}

.authentication-wrapper .authentication-image-model {
  width: 768px;
}

.authentication-wrapper.authentication-cover {
  align-items: flex-start;
}

.authentication-wrapper.authentication-cover .authentication-inner {
  min-height: 100vh;
  position: relative;
}

.authentication-wrapper.authentication-cover .authentication-image {
  inset-inline-start: unset;
}

.authentication-wrapper.authentication-cover .authentication-image-tree {
  position: absolute;
  bottom: 3rem;
  left: 3rem;
}

.authentication-wrapper .auth-input-wrapper .auth-input {
  max-width: 50px;
  padding-left: 0.4rem;
  padding-right: 0.4rem;
  font-size: 150%;
}

@media (max-width: 575.98px) {
  .authentication-wrapper .auth-input-wrapper .auth-input {
    font-size: 1.25rem;
  }
}

.light-style .authentication-wrapper .authentication-bg {
  background-color: #fff;
}
