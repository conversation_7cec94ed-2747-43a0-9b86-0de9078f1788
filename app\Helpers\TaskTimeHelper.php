<?php

namespace App\Helpers;

use App\Models\TaskTimeLog;
use Carbon\Carbon;

class TaskTimeHelper
{
    public static function calculateTotalTaskTime($taskId)
    {
        $logs = TaskTimeLog::where('task_id', $taskId)
            ->orderBy('created_at')
            ->get();

        $totalSeconds = 0;

        // Only process pairs, ignore last if odd
        $pairCount = floor($logs->count() / 2) * 2;

        for ($i = 0; $i < $pairCount; $i += 2) {
            $startTime = Carbon::createFromTimeString($logs[$i]->time);
            $endTime = Carbon::createFromTimeString($logs[$i + 1]->time);

            $diff = $endTime->diffInSeconds($startTime);
            $totalSeconds += $diff;
        }

        $totalHours = floor($totalSeconds / 3600);
        $totalMinutes = floor(($totalSeconds % 3600) / 60);

        return [
            'hours' => $totalHours,
            'minutes' => $totalMinutes,
            'formatted' => "{$totalHours} hrs {$totalMinutes} min",
        ];
    }
}
