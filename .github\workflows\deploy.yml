name: Deploy Laravel Project via FTP

on:
  push:
    branches:
      - main # Or any branch you want to trigger deployment

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2' # Ensure the PHP version matches your server

      - name: Install Composer dependencies
        run: composer install --no-dev --prefer-dist --no-progress --no-interaction

      - name: Upload files via FTP
        uses: SamKirkland/FTP-Deploy-Action@4.3.2
        with:
          server: ${{ secrets.FTP_HOST }}
          username: ${{ secrets.FTP_USERNAME }}
          password: ${{ secrets.FTP_PASSWORD }}
          local-dir: ./ # Root of your Laravel project
          server-dir: /hrms.skysphereinfosoft.com # Change this to your target server directory

      - name: Set permissions (optional)
        run: |
          curl -u ${{ secrets.FTP_USERNAME }}:${{ secrets.FTP_PASSWORD }} ftp://${{ secrets.FTP_HOST }} \
          --quote 'SITE CHMOD 755 /public_html/storage' \
          --quote 'SITE CHMOD 755 /public_html/bootstrap/cache'
